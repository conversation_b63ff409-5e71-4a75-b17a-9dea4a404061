"use client";
import "./globals.css";
import "../styles/payment.css";
import { ThemeProvider } from "../../themeContext";
import NavBar from "@/components/navbar";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { metadata } from "./layoout.server";
import { Providers } from "@/lib/Providers";
import GlobalSignInButton from "@/components/GlobalSignInButton";
import { createConfig, WagmiProvider } from "wagmi";
import getRpc, { CHAIN, NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID } from "@/lib/constant";
import { injected, walletConnect } from "wagmi/connectors";
import React from "react";
import FirebaseProvider from "@/context/FirebaseContext";
import LoadingProvider from "@/context/LoadingContext";
import GoogleMapsProvider from "@/context/GoogleMapsContext";
import FilterProvider from "@/context/FilterContext";
import { AuthProvider } from "@/context/AuthContext";
import AuthWatcher from "@/components/AuthProvider";

// Create configuration outside component to avoid recreation on every render
const connectors = [
  injected(),
  walletConnect({
    projectId: NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID,
  }),
];

const config = createConfig({
  chains: [CHAIN],
  transports: {
    [CHAIN.id]: getRpc({ mainnet: true }),
  },
  connectors,
});

// Create QueryClient outside component to avoid recreation on every render
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      refetchOnWindowFocus: false,
    },
  },
});

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <head>
        <title>{metadata?.title || "Default Title"}</title>
        <meta name="description" content={metadata.description} />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, viewport-fit=cover" />
      </head>

      <body>
        <Providers>
          <LoadingProvider>
            <FirebaseProvider>
              <GoogleMapsProvider>
                <FilterProvider>
                  <AuthProvider>
                    <WagmiProvider config={config}>
                      <QueryClientProvider client={queryClient}>
                        <ThemeProvider>
                          <NavBar />
                          <AuthWatcher>{children}</AuthWatcher>
                          <GlobalSignInButton />
                        </ThemeProvider>
                      </QueryClientProvider>
                    </WagmiProvider>
                  </AuthProvider>
                </FilterProvider>
              </GoogleMapsProvider>
            </FirebaseProvider>
          </LoadingProvider>
        </Providers>
      </body>
    </html>
  );
}
