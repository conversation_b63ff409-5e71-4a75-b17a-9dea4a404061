"use client";
import useAuth from "@/hook";
import { Timestamp } from "firebase/firestore";
import { useEffect, useState, useRef } from "react";
import { Check, FilePlus, Trash, X, Loader } from "react-feather";
import { updateUser } from "@/services/usersServices";
import { ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { initFirebase } from "../../../../firebaseConfig";
import { getPostById, updatePost } from "@/services/postService";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import LocationDropdown from "@/components/LocationDropdown";

// Define TypeScript interfaces
interface Post {
  id: string;
  category: string;
  about_project: string;
  hashtags: string[];
  geotags: string[];
  postFile: string;
  mediaType: "image" | "video";
  starred?: number;
  added_at?: any;
  updated_at?: any;
  userId?: string;
}

interface EditPostProps {
  postId: string;
  setIsOpenDelete: (isOpen: boolean) => void;
  onSuccess?: () => void;
}

const EditPost = ({ postId, setIsOpenDelete, onSuccess }: EditPostProps) => {
  const [category, setCategory] = useState<string | null>(null);
  const [about, setAbout] = useState("");
  const [hashtags, setHashtags] = useState<string[]>([]);
  const [geoTags, setGeoTags] = useState<string[]>([]);
  const [media, setMedia] = useState<File | null>(null);
  const [isMedia, setIsMedia] = useState(false);
  const [mediaURL, setMediaURL] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [post, setPost]: any = useState<Post | null>(null);
  const UPLOAD_STATUS = {
    IDLE: "idle" as const,
    UPLOADING: "uploading" as const,
    SUCCESS: "success" as const,
  };

  type UploadStatus = (typeof UPLOAD_STATUS)[keyof typeof UPLOAD_STATUS];
  const [uploadStatus, setUploadStatus] = useState<UploadStatus>(UPLOAD_STATUS.IDLE);

  // Google location API states
  const autocompleteService = useRef<google.maps.places.AutocompleteService | null>(null);
  const placesServiceLoaded = useRef(false);
  const [placePredictions, setPlacePredictions] = useState<
    google.maps.places.AutocompletePrediction[]
  >([]);
  const [showPlacesDropdown, setShowPlacesDropdown] = useState(false);
  const locationInputRef = useRef<HTMLInputElement>(null);
  const [isPersonalInfoSaving, setIsPersonalInfoSaving] = useState(false);
  const [personalInfoSaveSuccess, setPersonalInfoSaveSuccess] = useState(false);
  const [locationInput, setLocationInput] = useState("");
  const [hashtagInput, setHashtagInput] = useState("");

  const auth = useAuth();

  // Effect to handle success state
  useEffect(() => {
    if (uploadStatus === UPLOAD_STATUS.SUCCESS) {
      // Wait 1.5 seconds before closing the modal to show success state
      const timer = setTimeout(() => {
        setIsOpenDelete(false);
        if (onSuccess) onSuccess();
        // Reload the page to ensure content is updated
        window.location.reload();
      }, 1500);

      return () => clearTimeout(timer);
    }
  }, [uploadStatus, setIsOpenDelete, onSuccess]);

  // Initialize Google Maps API
  useEffect(() => {
    if (window.google && window.google.maps && !placesServiceLoaded.current) {
      placesServiceLoaded.current = true;
      autocompleteService.current = new google.maps.places.AutocompleteService();
    }
  }, []);

  // Handle location input change and fetch place predictions
  const handleLocationInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setLocationInput(value);

    if (value.trim() === "") {
      setPlacePredictions([]);
      setShowPlacesDropdown(false);
      return;
    }

    if (autocompleteService.current && placesServiceLoaded.current) {
      autocompleteService.current.getPlacePredictions(
        {
          input: value,
          types: ["(regions)"],
        },
        (predictions, status) => {
          if (status === google.maps.places.PlacesServiceStatus.OK && predictions) {
            setPlacePredictions(predictions);
            setShowPlacesDropdown(true);
          } else {
            setPlacePredictions([]);
            setShowPlacesDropdown(false);
          }
        }
      );
    }
  };

  // Select a place from the dropdown
  const selectPlace = (place: google.maps.places.AutocompletePrediction) => {
    if (!geoTags.includes(place.description)) {
      setGeoTags([...geoTags, place.description]);
    }
    setLocationInput("");
    setShowPlacesDropdown(false);
  };

  // Fetch post by ID and populate form fields
  useEffect(() => {
    const fetchPostData = async () => {
      if (postId) {
        setIsLoading(true);
        try {
          const response = await getPostById(postId);
          if (response?.success && response.post) {
            const postData = response.post;
            setPost(postData);

            // Populate form fields with existing data
            setCategory(postData.category || null);
            setAbout(postData.about_project || "");
            setHashtags(postData.hashtags || []);
            setGeoTags(postData.geotags || []);

            // Set media URL from existing post
            if (postData.postFile) {
              setMediaURL(postData.postFile);
              setIsMedia(true);
            }
          } else {
            alert("Failed to fetch post data");
          }
        } catch (error) {
          console.error("Error fetching post:", error);
          alert("An error occurred while fetching the post");
        } finally {
          setIsLoading(false);
        }
      }
    };

    fetchPostData();
  }, [postId]);

  const handleSubmit = async (e?: React.FormEvent) => {
    if (e) e.preventDefault();

    if (!category) {
      alert("Please select a category.");
      return;
    }

    setUploadStatus(UPLOAD_STATUS.UPLOADING);

    try {
      let updatedMediaURL = mediaURL;

      // Only upload new media if it has changed
      if (media) {
        const { storage } = await initFirebase();
        const storageRef = ref(storage, `images/${Date.now()}_${media.name}`);
        const uploadTaskSnapshot = await uploadBytes(storageRef, media);
        updatedMediaURL = await getDownloadURL(uploadTaskSnapshot.ref);
      }

      if (!updatedMediaURL) {
        alert("Media is required.");
        setUploadStatus(UPLOAD_STATUS.IDLE);
        return;
      }

      // Prepare the updated post data
      const updatedData: Partial<Post> = {
        id: postId,
        category: category || "Uncategorized",
        about_project: about,
        hashtags,
        geotags: geoTags,
        postFile: updatedMediaURL,
        mediaType: media
          ? media.type.includes("image")
            ? "image"
            : "video"
          : post?.mediaType || "image",
        updated_at: Timestamp.fromDate(new Date()),
      };

      // Use the proper update post service
      const response = await updatePost(postId, updatedData);

      if (response && response.success) {
        setUploadStatus(UPLOAD_STATUS.SUCCESS);
      } else {
        setUploadStatus(UPLOAD_STATUS.IDLE);
        alert("Failed to update post: " + (response?.success || "Unknown error"));
      }
    } catch (error) {
      console.error("Error updating post:", error);
      setUploadStatus(UPLOAD_STATUS.IDLE);
      alert("An error occurred while updating the post");
    }
  };

  const handleMediaUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setMedia(event.target.files[0]);
      setIsMedia(true);
      setMediaURL(null); // Clear the previous media URL when a new file is selected
    }
  };

  const handleHashtagSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (hashtagInput.trim()) {
      setHashtags([...hashtags, hashtagInput.trim()]);
      setHashtagInput("");
    }
  };

  const handleHashtagKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" || e.keyCode === 13) {
      e.preventDefault();
      handleHashtagSubmit(e);
    }
  };

  const addGeoTag = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if ((e.key === "Enter" || e.keyCode === 13) && e.currentTarget.value.trim()) {
      e.preventDefault();
      const newTag = e.currentTarget.value.trim();
      if (!geoTags.includes(newTag)) {
        setGeoTags([...geoTags, newTag]);
      }
      e.currentTarget.value = ""; // Clear input
    }
  };

  const removeHashtag = (tag: string) => {
    setHashtags(hashtags.filter((hashtag) => hashtag !== tag));
  };

  const removeGeoTag = (tag: string) => {
    setGeoTags(geoTags.filter((geoTag) => geoTag !== tag));
  };

  const removeMedia = () => {
    setMedia(null);
    setIsMedia(false);
    setMediaURL(null);
  };

  // Function to render the appropriate content based on upload status
  const renderContent = () => {
    if (uploadStatus === UPLOAD_STATUS.SUCCESS) {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-green-100 rounded-full p-4 mb-4">
            <Check size={48} className="text-green-500" />
          </div>
          <h2 className="text-xl font-bold mb-2">Post Updated Successfully!</h2>
          <p className="text-gray-500">
            Your post has been updated and the changes will be visible on your profile.
          </p>
        </div>
      );
    }

    if (uploadStatus === UPLOAD_STATUS.UPLOADING) {
      return (
        <div className="flex flex-col items-center justify-center h-[60vh]">
          <div className="bg-blue-50 rounded-full p-4 mb-4">
            <Loader size={48} className="text-primary animate-spin" />
          </div>
          <h2 className="text-xl font-bold mb-2">Updating Your Post...</h2>
          <p className="text-gray-500">
            Please wait while we update your post. This may take a moment.
          </p>
        </div>
      );
    }

    // Default form content - matches CreatePost structure
    return (
      <>
        {/* Fixed Header - matches CreatePost structure */}
        <div className="flex flex-row justify-between items-center mb-4 sticky top-0 bg-white z-[9999]">
          <div onClick={() => setIsOpenDelete(false)} className="cursor-pointer">
            <X />
          </div>
          <h2 className="text-xl font-bold">Edit Post</h2>
          <p
            onClick={handleSubmit}
            className={
              category && about && hashtags.length > 0 && geoTags.length > 0 && (media || mediaURL)
                ? "font-bold text-primary cursor-pointer"
                : "font-bold text-borderColor cursor-not-allowed"
            }
          >
            Save
          </p>
        </div>

        {/* Scrollable Content - matches CreatePost structure */}
        <div className="bg-gray-50">
          <div className="bg-white rounded-md p-2 py-4">
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* About Project */}
              <div>
                <label htmlFor="about" className="text-primary mb-2 font-[600] block text-start">
                  About Project
                </label>
                <textarea
                  id="about"
                  value={about}
                  onChange={(e) => setAbout(e.target.value)}
                  placeholder="What was this project about?"
                  className="w-full p-2 border border-gray-300 rounded-md resize-none"
                  rows={4}
                ></textarea>
                {/* <p className="text-sm text-gray-500">
                    {about.length}/150 characters
                  </p> */}
              </div>

              {/* Media */}
              <div>
                <label className="text-primary mb-2 font-[600] block text-start">Media</label>
                {!isMedia ? (
                  <div className="w-full">
                    <label htmlFor="media-upload" className="row gap-4 cursor-pointer">
                      <span className="bg-primary text-white p-2 rounded-xl h-[60px] w-[100px] row items-center justify-center">
                        <FilePlus />
                      </span>
                      <div className="flex flex-col">
                        <span className="text-subtitle text-sm text-start">
                          File size max 4MB per photo
                        </span>
                        <span className="text-subtitle mt-1 text-sm text-start">
                          File size max 8MB / 15 seconds per video
                        </span>
                      </div>
                      <input
                        type="file"
                        id="media-upload"
                        className="hidden"
                        accept="image/*,video/*"
                        onChange={handleMediaUpload}
                      />
                    </label>
                  </div>
                ) : (
                  <div className="mt-2 relative">
                    {/* Show media preview */}
                    {media ? (
                      <img
                        src={URL.createObjectURL(media)}
                        alt="Media Preview"
                        className="w-full h-auto rounded-md max-h-[300px] object-cover"
                      />
                    ) : (
                      mediaURL && (
                        <img
                          src={mediaURL}
                          alt="Current Media"
                          className="w-full h-auto rounded-md max-h-[300px] object-cover"
                        />
                      )
                    )}
                    <div
                      className="bg-white p-2 text-primary rounded-full absolute top-2 right-2 cursor-pointer"
                      onClick={removeMedia}
                    >
                      <Trash />
                    </div>
                  </div>
                )}
              </div>

              {/* Hashtags */}
              <div>
                <label className="text-primary mb-2 font-[600] block text-start">
                  Hashtags<span className="text-primary">*</span>
                </label>
                <div className="border-2 rounded-lg p-3">
                  <div className="flex flex-wrap gap-2 mb-2">
                    {hashtags.map((tag, index) => (
                      <span
                        key={index}
                        className="bg-[#EEEEEE] text-[#404040] px-2 py-1 rounded-md flex items-center space-x-1"
                      >
                        {tag}
                        <button
                          type="button"
                          onClick={() => removeHashtag(tag)}
                          className="text-primary pt-[2px] pl-2"
                        >
                          <X size={15} strokeWidth="3px" />
                        </button>
                      </span>
                    ))}
                  </div>
                  <form onSubmit={handleHashtagSubmit} className="m-0 p-0">
                    <input
                      type="text"
                      placeholder="Enter hashtags here"
                      className="w-full pt-2 border-none border-gray-300 rounded-md outline-none"
                      value={hashtagInput}
                      onChange={(e) => setHashtagInput(e.target.value)}
                      onKeyDown={handleHashtagKeyDown}
                      enterKeyHint="done"
                    />
                  </form>
                </div>
                <p className="text-sm text-gray-500">
                  Hashtag is a word, which allows users to discover your posts and services.
                </p>
              </div>

              {/* Geo-tags with Google Autocomplete */}
              <div>
                <label className="text-primary mb-1 font-[600] block text-start">Geo-tag</label>
                <div className="border-2 rounded-lg p-3">
                  <div className="flex flex-wrap gap-2 mb-2">
                    {geoTags.map((tag, index) => (
                      <span
                        key={index}
                        className="bg-[#EEEEEE] text-[#404040] px-2 py-1 rounded-md flex items-center space-x-1"
                      >
                        {tag}
                        <button
                          type="button"
                          onClick={() => removeGeoTag(tag)}
                          className="text-primary pt-[2px] pl-2"
                        >
                          <X size={15} strokeWidth="3px" />
                        </button>
                      </span>
                    ))}
                  </div>

                  <div className="relative">
                    <Input
                      ref={locationInputRef}
                      placeholder="Start typing location name..."
                      className={`resize-none h-[40px] outline-none text-lg text-primary ${
                        isPersonalInfoSaving || personalInfoSaveSuccess
                          ? "opacity-70 cursor-not-allowed"
                          : ""
                      }`}
                      value={locationInput}
                      onChange={
                        !isPersonalInfoSaving && !personalInfoSaveSuccess
                          ? handleLocationInputChange
                          : undefined
                      }
                      onKeyDown={(e) => {
                        if (e.key === "Enter" && locationInput.trim()) {
                          e.preventDefault();
                          if (!geoTags.includes(locationInput.trim())) {
                            setGeoTags([...geoTags, locationInput.trim()]);
                          }
                          setLocationInput("");
                          setShowPlacesDropdown(false);
                        }
                      }}
                      disabled={isPersonalInfoSaving || personalInfoSaveSuccess}
                      aria-label="location"
                    />
                    <LocationDropdown
                      predictions={placePredictions}
                      onSelect={selectPlace}
                      inputRef={locationInputRef}
                      isVisible={
                        !isPersonalInfoSaving &&
                        !personalInfoSaveSuccess &&
                        showPlacesDropdown &&
                        placePredictions.length > 0
                      }
                    />
                  </div>
                </div>
              </div>
            </form>
          </div>
        </div>
      </>
    );
  };

  if (isLoading) {
    return <div className="text-center p-4">Loading post data...</div>;
  }

  return renderContent();
};

export default EditPost;
