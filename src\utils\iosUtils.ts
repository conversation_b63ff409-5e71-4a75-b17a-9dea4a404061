/**
 * Simple iOS utilities following the same pattern as BrowserCard.tsx
 * This approach uses CSS-only solutions without complex JavaScript handling
 */

/**
 * Simple iOS detection utility
 */
export const isIOS = (): boolean => {
  if (typeof window === "undefined") return false;

  return (
    /iPad|iPhone|iPod/.test(navigator.userAgent) ||
    (navigator.platform === "MacIntel" && navigator.maxTouchPoints > 1)
  );
};

/**
 * Get iOS-safe font size for inputs/textareas to prevent zoom
 */
export const getIOSFontSize = (): string => {
  return "16px"; // Always use 16px to prevent iOS zoom
};

/**
 * Get iOS-safe styles for textarea/input elements
 * Following the same pattern as BrowserCard.tsx
 */
export const getIOSInputStyles = () => {
  return {
    fontSize: "16px", // Prevent iOS zoom
    WebkitAppearance: "none" as const,
    borderRadius: "8px",
  };
};

/**
 * Get iOS-safe styles specifically for search inputs
 */
export const getIOSSearchInputStyles = () => {
  return {
    fontSize: "16px", // Prevent iOS zoom
    WebkitAppearance: "none" as const,
    borderRadius: "6px", // Slightly smaller radius for search inputs
  };
};

/**
 * Simple hook for iOS-safe textarea handling
 * No complex state management, just basic styles
 */
export const useIOSTextareaStyles = () => {
  return {
    styles: getIOSInputStyles(),
    className: "ios-textarea-safe",
  };
};

/**
 * Enhanced iOS header positioning for search drawers
 * Ensures header stays visible when virtual keyboard appears
 */
export const getIOSHeaderStyles = () => {
  if (!isIOS()) return {};

  return {
    position: "sticky" as const,
    top: 0,
    backgroundColor: "white",
    zIndex: 9999,
    WebkitTransform: "translateZ(0)",
    transform: "translateZ(0)",
    WebkitBackfaceVisibility: "hidden" as const,
    backfaceVisibility: "hidden" as const,
  };
};
